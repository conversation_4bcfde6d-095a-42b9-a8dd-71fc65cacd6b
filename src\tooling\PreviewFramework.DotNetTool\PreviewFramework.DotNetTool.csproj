<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>preview-devtools</ToolCommandName>
  </PropertyGroup>

  <PropertyGroup>
    <PackageId>PreviewFramework.DevTools</PackageId>
    <Title>PreviewFramework DevTools</Title>
    <PackageDescription>A .NET global tool for launching PreviewFramework DevTools - a visual development environment for UI component previews</PackageDescription>
    <PackageTags>preview;ui;components;devtools;maui;wpf;uno;tool</PackageTags>
  </PropertyGroup>

  <!-- Project reference to ensure build order, but exclude from package -->
  <ItemGroup>
    <ProjectReference Include="../PreviewFramework.DevToolsApp/PreviewFramework.DevToolsApp.csproj">
      <IncludeAssets>none</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

  <!-- Include DevTools files directly in the project -->
  <ItemGroup>
    <Content Include="../../../bin/PreviewFramework.DevToolsApp/Debug/net9.0-desktop/**/*"
             PackagePath="tools/app/%(RecursiveDir)%(Filename)%(Extension)"
             Pack="true" />
  </ItemGroup>

</Project>
